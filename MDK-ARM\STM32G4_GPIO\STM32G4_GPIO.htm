<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [STM32G4_GPIO\STM32G4_GPIO.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image STM32G4_GPIO\STM32G4_GPIO.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6140002: Last Updated: Tue Jun 24 18:09:18 2025
<BR><P>
<H3>Maximum Stack Usage =        524 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; MX_ADC1_Init &rArr; HAL_ADCEx_InjectedConfigChannel &rArr; LL_ADC_SetOffset
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[70]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[44]">COMP4_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[44]">COMP4_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1b]">ADC1_2_IRQHandler</a> from stm32g4xx_it.o(.text.ADC1_2_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from stm32g4xx_it.o(.text.BusFault_Handler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[43]">COMP1_2_3_IRQHandler</a> from stm32g4xx_it.o(.text.COMP1_2_3_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[44]">COMP4_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[4e]">CORDIC_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[45]">CRS_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[15]">DMA1_Channel1_IRQHandler</a> from stm32g4xx_it.o(.text.DMA1_Channel1_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[16]">DMA1_Channel2_IRQHandler</a> from stm32g4xx_it.o(.text.DMA1_Channel2_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[17]">DMA1_Channel3_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[18]">DMA1_Channel4_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[19]">DMA1_Channel5_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel6_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[3d]">DMA2_Channel1_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[3e]">DMA2_Channel2_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[3f]">DMA2_Channel3_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[40]">DMA2_Channel4_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[41]">DMA2_Channel5_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[4d]">DMA2_Channel6_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[4c]">DMAMUX_OVR_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from stm32g4xx_it.o(.text.DebugMon_Handler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[31]">EXTI15_10_IRQHandler</a> from stm32g4xx_it.o(.text.EXTI15_10_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[20]">EXTI9_5_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[1e]">FDCAN1_IT0_IRQHandler</a> from stm32g4xx_it.o(.text.FDCAN1_IT0_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[1f]">FDCAN1_IT1_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[4f]">FMAC_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[47]">FPU_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from stm32g4xx_it.o(.text.HardFault_Handler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[29]">I2C1_ER_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[28]">I2C1_EV_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[2b]">I2C2_ER_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[2a]">I2C2_EV_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[4b]">I2C3_ER_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[4a]">I2C3_EV_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[38]">LPTIM1_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[49]">LPUART1_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from stm32g4xx_it.o(.text.MemManage_Handler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from stm32g4xx_it.o(.text.NMI_Handler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[b]">PVD_PVM_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from stm32g4xx_it.o(.text.PendSV_Handler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[48]">RNG_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[32]">RTC_Alarm_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[c]">RTC_TAMP_LSECSS_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[d]">RTC_WKUP_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[46]">SAI1_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[2c]">SPI1_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[2d]">SPI2_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[39]">SPI3_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from stm32g4xx_it.o(.text.SVC_Handler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from stm32g4xx_it.o(.text.SysTick_Handler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[50]">SystemInit</a> from system_stm32g4xx.o(.text.SystemInit) referenced from startup_stm32g431xx.o(.text)
 <LI><a href="#[21]">TIM1_BRK_TIM15_IRQHandler</a> from stm32g4xx_it.o(.text.TIM1_BRK_TIM15_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[24]">TIM1_CC_IRQHandler</a> from stm32g4xx_it.o(.text.TIM1_CC_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[23]">TIM1_TRG_COM_TIM17_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[22]">TIM1_UP_TIM16_IRQHandler</a> from stm32g4xx_it.o(.text.TIM1_UP_TIM16_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[25]">TIM2_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[26]">TIM3_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[27]">TIM4_IRQHandler</a> from stm32g4xx_it.o(.text.TIM4_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[3b]">TIM6_DAC_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[3c]">TIM7_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[34]">TIM8_BRK_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[37]">TIM8_CC_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[36]">TIM8_TRG_COM_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[35]">TIM8_UP_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[3a]">UART4_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[52]">UART_DMAAbortOnError</a> from stm32g4xx_hal_uart.o(.text.UART_DMAAbortOnError) referenced 2 times from stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
 <LI><a href="#[55]">UART_DMAError</a> from stm32g4xx_hal_uart.o(.text.UART_DMAError) referenced 2 times from stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
 <LI><a href="#[53]">UART_DMATransmitCplt</a> from stm32g4xx_hal_uart.o(.text.UART_DMATransmitCplt) referenced 2 times from stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
 <LI><a href="#[54]">UART_DMATxHalfCplt</a> from stm32g4xx_hal_uart.o(.text.UART_DMATxHalfCplt) referenced 2 times from stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
 <LI><a href="#[42]">UCPD1_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[2e]">USART1_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[2f]">USART2_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[30]">USART3_IRQHandler</a> from stm32g4xx_it.o(.text.USART3_IRQHandler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[33]">USBWakeUp_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[1c]">USB_HP_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[1d]">USB_LP_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from stm32g4xx_it.o(.text.UsageFault_Handler) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32g431xx.o(.text) referenced from startup_stm32g431xx.o(RESET)
 <LI><a href="#[51]">__main</a> from __main.o(!!!main) referenced from startup_stm32g431xx.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[51]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(.text)
</UL>
<P><STRONG><a name="[56]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[58]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[176]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[177]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[59]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[178]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[61]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[5a]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[5c]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_argv_1 &rArr; __ARM_argv_veneer &rArr; __ARM_get_argv &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_argv_veneer
</UL>

<P><STRONG><a name="[179]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[17a]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[17b]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[17c]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[17d]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[17e]"></a>__rt_lib_init_heapregion_libspace_used</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[17f]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[180]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[181]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[182]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[183]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[184]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[185]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[186]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[187]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[188]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[189]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[18a]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[18b]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[18c]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[66]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[18d]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[18e]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[18f]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[190]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[191]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[192]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[193]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[57]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[194]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[5e]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[60]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[195]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[62]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 524 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; MX_ADC1_Init &rArr; HAL_ADCEx_InjectedConfigChannel &rArr; LL_ADC_SetOffset
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[196]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[71]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[65]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[197]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[67]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[5d]"></a>__ARM_argv_veneer</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, argv_veneer.o(.emb_text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __ARM_argv_veneer &rArr; __ARM_get_argv &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_get_argv
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_argv_1
</UL>

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>COMP4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;COMP4_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;COMP4_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CORDIC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>CRS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>DMA2_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>DMA2_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>DMA2_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMAMUX_OVR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>FDCAN1_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>FMAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>LPTIM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>LPUART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_PVM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>RTC_TAMP_LSECSS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_TRG_COM_TIM17_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UCPD1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>USB_HP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>USB_LP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g431xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32g431xx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[166]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[198]"></a>_ll_udiv</STRONG> (Thumb, 240 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[15d]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Load_UART_Tx_Buff
</UL>

<P><STRONG><a name="[6a]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[199]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[ca]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC3_Init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC1_Init
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_MspInit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[19a]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[19b]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[19c]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[19d]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[19e]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[19f]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[6c]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[6e]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[6b]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[1a0]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1a1]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1a2]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[6d]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[1a3]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[1a4]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[1a5]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[6f]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1a6]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[5f]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[64]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[72]"></a>__sig_exit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, defsig_exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[68]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>

<P><STRONG><a name="[73]"></a>__default_signal_display</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, defsig_general.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
</UL>

<P><STRONG><a name="[74]"></a>_ttywrch</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, sys_wrch.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>

<P><STRONG><a name="[1a7]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1a8]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[75]"></a>__argv_alloc</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _get_argv_nomalloc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_get_argv
</UL>

<P><STRONG><a name="[1a9]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[69]"></a>__ARM_get_argv</STRONG> (Thumb, 268 bytes, Stack size 56 bytes, _get_argv_nomalloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __ARM_get_argv &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_command_string
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__argv_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_argv_veneer
</UL>

<P><STRONG><a name="[77]"></a>_sys_command_string</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, sys_command.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _sys_command_string
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_get_argv
</UL>

<P><STRONG><a name="[1aa]"></a>__use_no_heap_region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hrguard.o(.text), UNUSED)

<P><STRONG><a name="[1ab]"></a>__heap_region$guard</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hrguard.o(.text), UNUSED)

<P><STRONG><a name="[78]"></a>__rt_SIGRTMEM_inner</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, defsig_rtmem_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[76]"></a>__rt_SIGRTMEM</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtmem_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_get_argv
</UL>

<P><STRONG><a name="[1b]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.ADC1_2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = ADC1_2_IRQHandler &rArr; HAL_ADC_IRQHandler &rArr; HAL_ADCEx_InjectedConvCpltCallback &rArr; task_isr_10kHZ &rArr; FOC_Model_step &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>ADC_Disable</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, stm32g4xx_hal_adc.o(.text.ADC_Disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ADC_Disable &rArr; LL_ADC_IsEnabled
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsEnabled
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsDisableOngoing
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_Calibration_Start
</UL>

<P><STRONG><a name="[7f]"></a>ADC_Enable</STRONG> (Thumb, 280 bytes, Stack size 24 bytes, stm32g4xx_hal_adc.o(.text.ADC_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ADC_Enable &rArr; LL_ADC_IsEnabled
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsEnabled
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetCommonPathInternalCh
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart_IT
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart
</UL>

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32g4xx_it.o(.text.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>COMP1_2_3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.COMP1_2_3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = COMP1_2_3_IRQHandler &rArr; HAL_COMP_IRQHandler &rArr; HAL_COMP_TriggerCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Channel1_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.DMA1_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Channel2_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g4xx_it.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.EXTI15_10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = EXTI15_10_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[cc]"></a>Error_Handler</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, main.o(.text.Error_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_OPAMP3_Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_OPAMP2_Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_OPAMP1_Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FDCAN1_Init
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC3_Init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC1_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_COMP1_Init
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_MspInit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[1e]"></a>FDCAN1_IT0_IRQHandler</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, stm32g4xx_it.o(.text.FDCAN1_IT0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = FDCAN1_IT0_IRQHandler &rArr; HAL_FDCAN_IRQHandler &rArr; HAL_FDCAN_TxEventFifoCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_GetRxMessage
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>FOC_Model_step</STRONG> (Thumb, 2160 bytes, Stack size 64 bytes, foc_model.o(.text.FOC_Model_step))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = FOC_Model_step &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rate_scheduler
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVPWM
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floorf
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_isr_10kHZ
</UL>

<P><STRONG><a name="[8e]"></a>HALL_Get_Electrical_Angle</STRONG> (Thumb, 776 bytes, Stack size 24 bytes, hall_speed_pos_fdbk.o(.text.HALL_Get_Electrical_Angle))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HALL_Get_Electrical_Angle &rArr; HAL_TIM_ReadCapturedValue
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ReadCapturedValue
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
</UL>

<P><STRONG><a name="[91]"></a>HALL_Init_Electrical_Angle</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, hall_speed_pos_fdbk.o(.text.HALL_Init_Electrical_Angle))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HALL_Init_Electrical_Angle &rArr; HAL_GPIO_ReadPin
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_start
</UL>

<P><STRONG><a name="[92]"></a>HAL_ADCEx_Calibration_Start</STRONG> (Thumb, 228 bytes, Stack size 32 bytes, stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_ADCEx_Calibration_Start &rArr; ADC_Disable &rArr; LL_ADC_IsEnabled
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_StartCalibration
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsCalibrationOnGoing
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b9]"></a>HAL_ADCEx_EndOfSamplingCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_EndOfSamplingCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_ADCEx_EndOfSamplingCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[95]"></a>HAL_ADCEx_InjectedConfigChannel</STRONG> (Thumb, 2616 bytes, Stack size 296 bytes, stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 316<LI>Call Chain = HAL_ADCEx_InjectedConfigChannel &rArr; LL_ADC_SetOffset
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetSamplingTimeCommonConfig
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetOffsetState
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetOffsetSign
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetOffsetSaturation
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetOffset
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetCommonPathInternalCh
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetChannelSingleDiff
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetChannelSamplingTime
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsEnabled
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_IsConversionOngoing
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetOffsetChannel
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetCommonPathInternalCh
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[a3]"></a>HAL_ADCEx_InjectedConvCpltCallback</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, motor_board_func.o(.text.HAL_ADCEx_InjectedConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = HAL_ADCEx_InjectedConvCpltCallback &rArr; task_isr_10kHZ &rArr; FOC_Model_step &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_isr_10kHZ
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[c2]"></a>HAL_ADCEx_InjectedQueueOverflowCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedQueueOverflowCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_ADCEx_InjectedQueueOverflowCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[a5]"></a>HAL_ADCEx_InjectedStart</STRONG> (Thumb, 426 bytes, Stack size 40 bytes, stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_ADCEx_InjectedStart &rArr; ADC_Enable &rArr; LL_ADC_IsEnabled
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_StartConversion
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_IsConversionOngoing
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_GetTrigAuto
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetMultimode
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a9]"></a>HAL_ADCEx_InjectedStart_IT</STRONG> (Thumb, 518 bytes, Stack size 40 bytes, stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_ADCEx_InjectedStart_IT &rArr; ADC_Enable &rArr; LL_ADC_IsEnabled
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_StartConversion
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_IsConversionOngoing
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_GetTrigAuto
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetMultimode
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[be]"></a>HAL_ADCEx_LevelOutOfWindow2Callback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_LevelOutOfWindow2Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_ADCEx_LevelOutOfWindow2Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[bf]"></a>HAL_ADCEx_LevelOutOfWindow3Callback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_LevelOutOfWindow3Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_ADCEx_LevelOutOfWindow3Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[aa]"></a>HAL_ADCEx_MultiModeConfigChannel</STRONG> (Thumb, 384 bytes, Stack size 152 bytes, stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = HAL_ADCEx_MultiModeConfigChannel &rArr; LL_ADC_REG_IsConversionOngoing
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[ab]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 2128 bytes, Stack size 288 bytes, stm32g4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = HAL_ADC_ConfigChannel &rArr; LL_ADC_SetOffset
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetSamplingTimeCommonConfig
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetOffsetState
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetOffsetSign
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetOffsetSaturation
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetOffset
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetCommonPathInternalCh
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetChannelSingleDiff
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetChannelSamplingTime
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_SetSequencerRanks
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsEnabled
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_IsConversionOngoing
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetOffsetChannel
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetCommonPathInternalCh
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[bb]"></a>HAL_ADC_ConvCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_ADC_ConvCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[c1]"></a>HAL_ADC_ErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_ADC_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[16b]"></a>HAL_ADC_GetValue</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.HAL_ADC_GetValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_ADC_GetValue
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[79]"></a>HAL_ADC_IRQHandler</STRONG> (Thumb, 1214 bytes, Stack size 64 bytes, stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = HAL_ADC_IRQHandler &rArr; HAL_ADCEx_InjectedConvCpltCallback &rArr; task_isr_10kHZ &rArr; FOC_Model_step &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_LevelOutOfWindowCallback
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_LevelOutOfWindow3Callback
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_LevelOutOfWindow2Callback
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedQueueOverflowCallback
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_EndOfSamplingCallback
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsTriggerSourceSWStart
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_IsTriggerSourceSWStart
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_IsConversionOngoing
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetMultimode
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetMultiDMATransfer
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConvCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>

<P><STRONG><a name="[c3]"></a>HAL_ADC_Init</STRONG> (Thumb, 812 bytes, Stack size 40 bytes, stm32g4xx_hal_adc.o(.text.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetCommonClock
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsInternalRegulatorEnabled
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsEnabled
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsDeepPowerDownEnabled
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_INJ_IsConversionOngoing
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_EnableInternalRegulator
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_DisableDeepPowerDown
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[bd]"></a>HAL_ADC_LevelOutOfWindowCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_ADC_LevelOutOfWindowCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[c4]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 448 bytes, Stack size 144 bytes, adc.o(.text.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[d0]"></a>HAL_ADC_Start</STRONG> (Thumb, 464 bytes, Stack size 40 bytes, stm32g4xx_hal_adc.o(.text.HAL_ADC_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_ADC_Start &rArr; ADC_Enable &rArr; LL_ADC_IsEnabled
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_StartConversion
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetMultimode
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[82]"></a>HAL_COMP_IRQHandler</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, stm32g4xx_hal_comp.o(.text.HAL_COMP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_COMP_IRQHandler &rArr; HAL_COMP_TriggerCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_TriggerCallback
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_IsActiveFlag_0_31
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_ClearFlag_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;COMP1_2_3_IRQHandler
</UL>

<P><STRONG><a name="[d5]"></a>HAL_COMP_Init</STRONG> (Thumb, 490 bytes, Stack size 40 bytes, stm32g4xx_hal_comp.o(.text.HAL_COMP_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = HAL_COMP_Init &rArr; HAL_COMP_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_MspInit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_EnableRisingTrig_0_31
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_EnableIT_0_31
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_EnableFallingTrig_0_31
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_EnableEvent_0_31
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_DisableRisingTrig_0_31
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_DisableIT_0_31
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_DisableFallingTrig_0_31
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_DisableEvent_0_31
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_EXTI_ClearFlag_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_COMP1_Init
</UL>

<P><STRONG><a name="[d6]"></a>HAL_COMP_MspInit</STRONG> (Thumb, 122 bytes, Stack size 48 bytes, comp.o(.text.HAL_COMP_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_COMP_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>

<P><STRONG><a name="[d4]"></a>HAL_COMP_TriggerCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.HAL_COMP_TriggerCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_COMP_TriggerCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_IRQHandler
</UL>

<P><STRONG><a name="[df]"></a>HAL_DAC_ConfigChannel</STRONG> (Thumb, 932 bytes, Stack size 48 bytes, stm32g4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_DAC_ConfigChannel
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC3_Init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC1_Init
</UL>

<P><STRONG><a name="[e1]"></a>HAL_DAC_Init</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32g4xx_hal_dac.o(.text.HAL_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC3_Init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC1_Init
</UL>

<P><STRONG><a name="[e2]"></a>HAL_DAC_MspInit</STRONG> (Thumb, 176 bytes, Stack size 48 bytes, dac.o(.text.HAL_DAC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
</UL>

<P><STRONG><a name="[13d]"></a>HAL_DMA_Abort</STRONG> (Thumb, 160 bytes, Stack size 8 bytes, stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[13b]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 200 bytes, Stack size 16 bytes, stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[83]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 352 bytes, Stack size 24 bytes, stm32g4xx_hal_dma.o(.text.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[e3]"></a>HAL_DMA_Init</STRONG> (Thumb, 346 bytes, Stack size 24 bytes, stm32g4xx_hal_dma.o(.text.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_DMA_Init &rArr; DMA_CalcDMAMUXChannelBaseAndMask
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcDMAMUXRequestGenBaseAndMask
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcDMAMUXChannelBaseAndMask
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[e6]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 256 bytes, Stack size 32 bytes, stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit_DMA
</UL>

<P><STRONG><a name="[e8]"></a>HAL_Delay</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, stm32g4xx_hal.o(.text.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f3]"></a>HAL_FDCAN_ErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_ErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_FDCAN_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[f2]"></a>HAL_FDCAN_ErrorStatusCallback</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_ErrorStatusCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_FDCAN_ErrorStatusCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[86]"></a>HAL_FDCAN_GetRxMessage</STRONG> (Thumb, 566 bytes, Stack size 40 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_GetRxMessage))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_FDCAN_GetRxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FDCAN1_IT0_IRQHandler
</UL>

<P><STRONG><a name="[e9]"></a>HAL_FDCAN_HighPriorityMessageCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_HighPriorityMessageCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_FDCAN_HighPriorityMessageCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[85]"></a>HAL_FDCAN_IRQHandler</STRONG> (Thumb, 712 bytes, Stack size 48 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_FDCAN_IRQHandler &rArr; HAL_FDCAN_TxEventFifoCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_TxFifoEmptyCallback
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_TxEventFifoCallback
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_TxBufferCompleteCallback
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_TxBufferAbortCallback
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_TimestampWraparoundCallback
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_TimeoutOccurredCallback
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_RxFifo1Callback
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_RxFifo0Callback
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_HighPriorityMessageCallback
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_ErrorStatusCallback
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FDCAN1_IT0_IRQHandler
</UL>

<P><STRONG><a name="[f4]"></a>HAL_FDCAN_Init</STRONG> (Thumb, 720 bytes, Stack size 24 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = HAL_FDCAN_Init &rArr; HAL_FDCAN_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_MspInit
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FDCAN_CalcultateRamBlockAddresses
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FDCAN1_Init
</UL>

<P><STRONG><a name="[f5]"></a>HAL_FDCAN_MspInit</STRONG> (Thumb, 196 bytes, Stack size 120 bytes, fdcan.o(.text.HAL_FDCAN_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = HAL_FDCAN_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_Init
</UL>

<P><STRONG><a name="[ec]"></a>HAL_FDCAN_RxFifo0Callback</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_RxFifo0Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_FDCAN_RxFifo0Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[ed]"></a>HAL_FDCAN_RxFifo1Callback</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_RxFifo1Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_FDCAN_RxFifo1Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[f1]"></a>HAL_FDCAN_TimeoutOccurredCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_TimeoutOccurredCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_FDCAN_TimeoutOccurredCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[f0]"></a>HAL_FDCAN_TimestampWraparoundCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_TimestampWraparoundCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_FDCAN_TimestampWraparoundCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[ea]"></a>HAL_FDCAN_TxBufferAbortCallback</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_TxBufferAbortCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_FDCAN_TxBufferAbortCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[ef]"></a>HAL_FDCAN_TxBufferCompleteCallback</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_TxBufferCompleteCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_FDCAN_TxBufferCompleteCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[eb]"></a>HAL_FDCAN_TxEventFifoCallback</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_TxEventFifoCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_FDCAN_TxEventFifoCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[ee]"></a>HAL_FDCAN_TxFifoEmptyCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_fdcan.o(.text.HAL_FDCAN_TxFifoEmptyCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_FDCAN_TxFifoEmptyCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_IRQHandler
</UL>

<P><STRONG><a name="[f7]"></a>HAL_GPIO_EXTI_Callback</STRONG> (Thumb, 44 bytes, Stack size 4 bytes, motor_board_func.o(.text.HAL_GPIO_EXTI_Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[84]"></a>HAL_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32g4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[cd]"></a>HAL_GPIO_Init</STRONG> (Thumb, 884 bytes, Stack size 44 bytes, stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_OPAMP_MspInit
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_MspInit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_MspInit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
</UL>

<P><STRONG><a name="[90]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HALL_Init_Electrical_Angle
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HALL_Get_Electrical_Angle
</UL>

<P><STRONG><a name="[151]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, stm32g4xx_hal_gpio.o(.text.HAL_GPIO_WritePin))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[7e]"></a>HAL_GetTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g4xx_hal.o(.text.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConfigChannel
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Disable
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
</UL>

<P><STRONG><a name="[15e]"></a>HAL_IncTick</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32g4xx_hal.o(.text.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[f8]"></a>HAL_Init</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, stm32g4xx_hal.o(.text.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fa]"></a>HAL_InitTick</STRONG> (Thumb, 140 bytes, Stack size 16 bytes, stm32g4xx_hal.o(.text.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[fb]"></a>HAL_MspInit</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32g4xx_hal_msp.o(.text.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_DisableUCPDDeadBattery
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[cf]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, stm32g4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_MspInit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_MspInit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
</UL>

<P><STRONG><a name="[ce]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 40 bytes, stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_GetPriorityGrouping
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_EncodePriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_MspInit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_MspInit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[f9]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_NVIC_SetPriorityGrouping &rArr; __NVIC_SetPriorityGrouping
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[103]"></a>HAL_OPAMP_Init</STRONG> (Thumb, 514 bytes, Stack size 24 bytes, stm32g4xx_hal_opamp.o(.text.HAL_OPAMP_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = HAL_OPAMP_Init &rArr; HAL_OPAMP_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_OPAMP_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_OPAMP3_Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_OPAMP2_Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_OPAMP1_Init
</UL>

<P><STRONG><a name="[104]"></a>HAL_OPAMP_MspInit</STRONG> (Thumb, 246 bytes, Stack size 48 bytes, opamp.o(.text.HAL_OPAMP_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = HAL_OPAMP_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_OPAMP_Init
</UL>

<P><STRONG><a name="[167]"></a>HAL_OPAMP_Start</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, stm32g4xx_hal_opamp.o(.text.HAL_OPAMP_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_OPAMP_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[161]"></a>HAL_PWREx_ControlVoltageScaling</STRONG> (Thumb, 440 bytes, Stack size 20 bytes, stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_PWREx_ControlVoltageScaling
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[fd]"></a>HAL_PWREx_DisableUCPDDeadBattery</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableUCPDDeadBattery))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>

<P><STRONG><a name="[cb]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 1124 bytes, Stack size 32 bytes, stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_MspInit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[105]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 810 bytes, Stack size 32 bytes, stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetSysClockFreqFromPLLSource
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[e0]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConfigChannel
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>

<P><STRONG><a name="[108]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[109]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[107]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 252 bytes, Stack size 24 bytes, stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[10a]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1984 bytes, Stack size 48 bytes, stm32g4xx_hal_rcc.o(.text.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[fc]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_SYSTICK_Config &rArr; SysTick_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[124]"></a>HAL_TIMEx_Break2Callback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_Break2Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_Break2Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[123]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_BreakCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[126]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_CommutCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[158]"></a>HAL_TIMEx_ConfigBreakDeadTime</STRONG> (Thumb, 304 bytes, Stack size 16 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIMEx_ConfigBreakDeadTime
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[157]"></a>HAL_TIMEx_ConfigBreakInput</STRONG> (Thumb, 400 bytes, Stack size 48 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_TIMEx_ConfigBreakInput
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[128]"></a>HAL_TIMEx_DirectionChangeCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DirectionChangeCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_DirectionChangeCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[127]"></a>HAL_TIMEx_EncoderIndexCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EncoderIndexCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_EncoderIndexCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[10c]"></a>HAL_TIMEx_HallSensor_Init</STRONG> (Thumb, 318 bytes, Stack size 64 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = HAL_TIMEx_HallSensor_Init &rArr; TIM_TI1_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_SetConfig
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2_SetConfig
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
</UL>

<P><STRONG><a name="[10d]"></a>HAL_TIMEx_HallSensor_MspInit</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_HallSensor_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_Init
</UL>

<P><STRONG><a name="[111]"></a>HAL_TIMEx_HallSensor_Start_IT</STRONG> (Thumb, 326 bytes, Stack size 24 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_TIMEx_HallSensor_Start_IT &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_start
</UL>

<P><STRONG><a name="[113]"></a>HAL_TIMEx_HallSensor_Stop_IT</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_TIMEx_HallSensor_Stop_IT &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_stop
</UL>

<P><STRONG><a name="[129]"></a>HAL_TIMEx_IndexErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_IndexErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_IndexErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[156]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 318 bytes, Stack size 20 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[114]"></a>HAL_TIMEx_PWMN_Start</STRONG> (Thumb, 386 bytes, Stack size 48 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_TIMEx_PWMN_Start &rArr; TIM_CCxNChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxNChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_start
</UL>

<P><STRONG><a name="[116]"></a>HAL_TIMEx_PWMN_Stop</STRONG> (Thumb, 210 bytes, Stack size 24 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_TIMEx_PWMN_Stop &rArr; TIM_CCxNChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxNChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_stop
</UL>

<P><STRONG><a name="[12a]"></a>HAL_TIMEx_TransitionErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_TransitionErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_TransitionErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[117]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 178 bytes, Stack size 24 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[118]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 266 bytes, Stack size 72 bytes, tim.o(.text.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[168]"></a>HAL_TIM_Base_Start</STRONG> (Thumb, 214 bytes, Stack size 12 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_TIM_Base_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[119]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 500 bytes, Stack size 56 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = HAL_TIM_ConfigClockSource &rArr; TIM_ETR_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[11e]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, motor_board_func.o(.text.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_TIM_IC_CaptureCallback &rArr; HALL_Get_Electrical_Angle &rArr; HAL_TIM_ReadCapturedValue
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HALL_Get_Electrical_Angle
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[11f]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 778 bytes, Stack size 24 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; HALL_Get_Electrical_Angle &rArr; HAL_TIM_ReadCapturedValue
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_TransitionErrorCallback
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_IndexErrorCallback
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_EncoderIndexCallback
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_DirectionChangeCallback
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_Break2Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_IRQHandler
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_UP_TIM16_IRQHandler
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_CC_IRQHandler
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_BRK_TIM15_IRQHandler
</UL>

<P><STRONG><a name="[12b]"></a>HAL_TIM_MspPostInit</STRONG> (Thumb, 212 bytes, Stack size 64 bytes, tim.o(.text.HAL_TIM_MspPostInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[120]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_OC_DelayElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[12c]"></a>HAL_TIM_PWM_ConfigChannel</STRONG> (Thumb, 432 bytes, Stack size 32 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_TIM_PWM_ConfigChannel &rArr; TIM_OC2_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2_SetConfig
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC6_SetConfig
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC5_SetConfig
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4_SetConfig
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3_SetConfig
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[132]"></a>HAL_TIM_PWM_Init</STRONG> (Thumb, 178 bytes, Stack size 24 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_TIM_PWM_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[133]"></a>HAL_TIM_PWM_MspInit</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_PWM_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
</UL>

<P><STRONG><a name="[121]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_PWM_PulseFinishedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[134]"></a>HAL_TIM_PWM_Start</STRONG> (Thumb, 612 bytes, Stack size 72 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_start
</UL>

<P><STRONG><a name="[135]"></a>HAL_TIM_PWM_Stop</STRONG> (Thumb, 386 bytes, Stack size 40 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_TIM_PWM_Stop &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_stop
</UL>

<P><STRONG><a name="[122]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[8f]"></a>HAL_TIM_ReadCapturedValue</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_ReadCapturedValue
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HALL_Get_Electrical_Angle
</UL>

<P><STRONG><a name="[125]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_TriggerCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[15b]"></a>HAL_UARTEx_DisableFifoMode</STRONG> (Thumb, 124 bytes, Stack size 12 bytes, stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_DisableFifoMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_UARTEx_DisableFifoMode
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
</UL>

<P><STRONG><a name="[13e]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, stm32g4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[142]"></a>HAL_UARTEx_RxFifoFullCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_RxFifoFullCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UARTEx_RxFifoFullCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[136]"></a>HAL_UARTEx_SetRxFifoThreshold</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetRxFifoThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UARTEx_SetRxFifoThreshold &rArr; UARTEx_SetNbDataToProcess
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
</UL>

<P><STRONG><a name="[138]"></a>HAL_UARTEx_SetTxFifoThreshold</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetTxFifoThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UARTEx_SetTxFifoThreshold &rArr; UARTEx_SetNbDataToProcess
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
</UL>

<P><STRONG><a name="[141]"></a>HAL_UARTEx_TxFifoEmptyCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_TxFifoEmptyCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UARTEx_TxFifoEmptyCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[13f]"></a>HAL_UARTEx_WakeupCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_WakeupCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UARTEx_WakeupCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[13c]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_uart.o(.text.HAL_UART_ErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UART_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[139]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 1396 bytes, Stack size 72 bytes, stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_EndTransmit_IT &rArr; HAL_UART_TxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_WakeupCallback
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_TxFifoEmptyCallback
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxFifoFullCallback
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[143]"></a>HAL_UART_Init</STRONG> (Thumb, 178 bytes, Stack size 16 bytes, stm32g4xx_hal_uart.o(.text.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AdvFeatureConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
</UL>

<P><STRONG><a name="[144]"></a>HAL_UART_MspInit</STRONG> (Thumb, 348 bytes, Stack size 120 bytes, usart.o(.text.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[148]"></a>HAL_UART_Transmit_DMA</STRONG> (Thumb, 280 bytes, Stack size 32 bytes, stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_UART_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Load_UART_Tx_Buff
</UL>

<P><STRONG><a name="[164]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMATransmitCplt
</UL>

<P><STRONG><a name="[165]"></a>HAL_UART_TxHalfCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32g4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UART_TxHalfCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMATxHalfCplt
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32g4xx_it.o(.text.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[149]"></a>MX_ADC1_Init</STRONG> (Thumb, 330 bytes, Stack size 120 bytes, adc.o(.text.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 436<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADCEx_InjectedConfigChannel &rArr; LL_ADC_SetOffset
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_MultiModeConfigChannel
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14a]"></a>MX_ADC2_Init</STRONG> (Thumb, 246 bytes, Stack size 104 bytes, adc.o(.text.MX_ADC2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 420<LI>Call Chain = MX_ADC2_Init &rArr; HAL_ADCEx_InjectedConfigChannel &rArr; LL_ADC_SetOffset
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14b]"></a>MX_COMP1_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, comp.o(.text.MX_COMP1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = MX_COMP1_Init &rArr; HAL_COMP_Init &rArr; HAL_COMP_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14c]"></a>MX_DAC1_Init</STRONG> (Thumb, 112 bytes, Stack size 64 bytes, dac.o(.text.MX_DAC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = MX_DAC1_Init &rArr; HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConfigChannel
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14d]"></a>MX_DAC3_Init</STRONG> (Thumb, 108 bytes, Stack size 64 bytes, dac.o(.text.MX_DAC3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = MX_DAC3_Init &rArr; HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConfigChannel
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14e]"></a>MX_DMA_Init</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, dma.o(.text.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14f]"></a>MX_FDCAN1_Init</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, fdcan.o(.text.MX_FDCAN1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = MX_FDCAN1_Init &rArr; HAL_FDCAN_Init &rArr; HAL_FDCAN_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[150]"></a>MX_GPIO_Init</STRONG> (Thumb, 228 bytes, Stack size 64 bytes, gpio.o(.text.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = MX_GPIO_Init &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[152]"></a>MX_OPAMP1_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, opamp.o(.text.MX_OPAMP1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = MX_OPAMP1_Init &rArr; HAL_OPAMP_Init &rArr; HAL_OPAMP_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_OPAMP_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[153]"></a>MX_OPAMP2_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, opamp.o(.text.MX_OPAMP2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = MX_OPAMP2_Init &rArr; HAL_OPAMP_Init &rArr; HAL_OPAMP_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_OPAMP_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[154]"></a>MX_OPAMP3_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, opamp.o(.text.MX_OPAMP3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = MX_OPAMP3_Init &rArr; HAL_OPAMP_Init &rArr; HAL_OPAMP_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_OPAMP_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[155]"></a>MX_TIM1_Init</STRONG> (Thumb, 482 bytes, Stack size 144 bytes, tim.o(.text.MX_TIM1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = MX_TIM1_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_ConfigBreakInput
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_ConfigBreakDeadTime
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[159]"></a>MX_TIM4_Init</STRONG> (Thumb, 190 bytes, Stack size 56 bytes, tim.o(.text.MX_TIM4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = MX_TIM4_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15a]"></a>MX_USART3_UART_Init</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, usart.o(.text.MX_USART3_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = MX_USART3_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_DisableFifoMode
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32g4xx_it.o(.text.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[15c]"></a>Motor_Load_UART_Tx_Buff</STRONG> (Thumb, 120 bytes, Stack size 56 bytes, motor_board_func.o(.text.Motor_Load_UART_Tx_Buff))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Motor_Load_UART_Tx_Buff &rArr; HAL_UART_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit_DMA
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_isr_1kHZ
</UL>

<P><STRONG><a name="[169]"></a>Motor_app_init</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, motor_struct.o(.text.Motor_app_init))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32g4xx_it.o(.text.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g4xx_it.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g4xx_it.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = SysTick_Handler &rArr; task_isr_1kHZ &rArr; motor_start &rArr; HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_isr_1kHZ
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[160]"></a>SystemClock_Config</STRONG> (Thumb, 122 bytes, Stack size 96 bytes, main.o(.text.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ControlVoltageScaling
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[50]"></a>SystemInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, system_stm32g4xx.o(.text.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(.text)
</UL>
<P><STRONG><a name="[21]"></a>TIM1_BRK_TIM15_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.TIM1_BRK_TIM15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = TIM1_BRK_TIM15_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; HALL_Get_Electrical_Angle &rArr; HAL_TIM_ReadCapturedValue
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.TIM1_CC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = TIM1_CC_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; HALL_Get_Electrical_Angle &rArr; HAL_TIM_ReadCapturedValue
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_UP_TIM16_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.TIM1_UP_TIM16_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = TIM1_UP_TIM16_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; HALL_Get_Electrical_Angle &rArr; HAL_TIM_ReadCapturedValue
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = TIM4_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; HALL_Get_Electrical_Angle &rArr; HAL_TIM_ReadCapturedValue
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[10e]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 408 bytes, Stack size 12 bytes, stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_Init
</UL>

<P><STRONG><a name="[112]"></a>TIM_CCxChannelCmd</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Stop
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_Stop_IT
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_Start_IT
</UL>

<P><STRONG><a name="[11a]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 52 bytes, Stack size 20 bytes, stm32g4xx_hal_tim.o(.text.TIM_ETR_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[110]"></a>TIM_OC2_SetConfig</STRONG> (Thumb, 374 bytes, Stack size 20 bytes, stm32g4xx_hal_tim.o(.text.TIM_OC2_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC2_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_Init
</UL>

<P><STRONG><a name="[10f]"></a>TIM_TI1_SetConfig</STRONG> (Thumb, 242 bytes, Stack size 28 bytes, stm32g4xx_hal_tim.o(.text.TIM_TI1_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = TIM_TI1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_Init
</UL>

<P><STRONG><a name="[145]"></a>UART_AdvFeatureConfig</STRONG> (Thumb, 330 bytes, Stack size 4 bytes, stm32g4xx_hal_uart.o(.text.UART_AdvFeatureConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = UART_AdvFeatureConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[147]"></a>UART_CheckIdleState</STRONG> (Thumb, 330 bytes, Stack size 40 bytes, stm32g4xx_hal_uart.o(.text.UART_CheckIdleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UART_CheckIdleState &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[146]"></a>UART_SetConfig</STRONG> (Thumb, 1414 bytes, Stack size 80 bytes, stm32g4xx_hal_uart.o(.text.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[162]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 272 bytes, Stack size 32 bytes, stm32g4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
</UL>

<P><STRONG><a name="[30]"></a>USART3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32g4xx_it.o(.text.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = USART3_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_EndTransmit_IT &rArr; HAL_UART_TxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32g4xx_it.o(.text.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g431xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>main</STRONG> (Thumb, 452 bytes, Stack size 88 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 524<LI>Call Chain = main &rArr; MX_ADC1_Init &rArr; HAL_ADCEx_InjectedConfigChannel &rArr; LL_ADC_SetOffset
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_app_init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_OPAMP3_Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_OPAMP2_Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_OPAMP1_Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FDCAN1_Init
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC3_Init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC1_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_COMP1_Init
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_OPAMP_Start
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_GetValue
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart_IT
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_Calibration_Start
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[16c]"></a>motor_start</STRONG> (Thumb, 112 bytes, Stack size 56 bytes, motor_board_func.o(.text.motor_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = motor_start &rArr; HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HALL_Init_Electrical_Angle
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_PWMN_Start
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_isr_1kHZ
</UL>

<P><STRONG><a name="[16d]"></a>motor_stop</STRONG> (Thumb, 128 bytes, Stack size 48 bytes, motor_board_func.o(.text.motor_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = motor_stop &rArr; HAL_TIM_PWM_Stop &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Stop
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_PWMN_Stop
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_HallSensor_Stop_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_isr_1kHZ
</UL>

<P><STRONG><a name="[a4]"></a>task_isr_10kHZ</STRONG> (Thumb, 804 bytes, Stack size 8 bytes, motor_board_func.o(.text.task_isr_10kHZ))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = task_isr_10kHZ &rArr; FOC_Model_step &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FOC_Model_step
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConvCpltCallback
</UL>

<P><STRONG><a name="[15f]"></a>task_isr_1kHZ</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, motor_board_func.o(.text.task_isr_1kHZ))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = task_isr_1kHZ &rArr; motor_start &rArr; HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_stop
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_start
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Load_UART_Tx_Buff
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[171]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[89]"></a>__hardfp_cosf</STRONG> (Thumb, 280 bytes, Stack size 8 bytes, cosf.o(i.__hardfp_cosf))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = __hardfp_cosf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FOC_Model_step
</UL>

<P><STRONG><a name="[8c]"></a>__hardfp_floorf</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, floorf.o(i.__hardfp_floorf))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FOC_Model_step
</UL>

<P><STRONG><a name="[8a]"></a>__hardfp_sinf</STRONG> (Thumb, 344 bytes, Stack size 16 bytes, sinf.o(i.__hardfp_sinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FOC_Model_step
</UL>

<P><STRONG><a name="[170]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[16f]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[172]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[16e]"></a>__mathlib_rredf2</STRONG> (Thumb, 316 bytes, Stack size 20 bytes, rredf.o(i.__mathlib_rredf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __mathlib_rredf2
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[88]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FOC_Model_step
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Load_UART_Tx_Buff
</UL>

<P><STRONG><a name="[173]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[16a]"></a>__aeabi_i2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ac]"></a>_dflt</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt), UNUSED)

<P><STRONG><a name="[175]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[5b]"></a>_fp_init</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[1ad]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[1ae]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[174]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[8b]"></a>SVPWM</STRONG> (Thumb, 320 bytes, Stack size 28 bytes, foc_model.o(.text.SVPWM))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SVPWM
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FOC_Model_step
</UL>

<P><STRONG><a name="[8d]"></a>rate_scheduler</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, foc_model.o(.text.rate_scheduler))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FOC_Model_step
</UL>

<P><STRONG><a name="[7d]"></a>LL_ADC_Disable</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_Disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Disable
</UL>

<P><STRONG><a name="[c6]"></a>LL_ADC_DisableDeepPowerDown</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_DisableDeepPowerDown))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_DisableDeepPowerDown
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[80]"></a>LL_ADC_Enable</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
</UL>

<P><STRONG><a name="[c8]"></a>LL_ADC_EnableInternalRegulator</STRONG> (Thumb, 30 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_EnableInternalRegulator))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_EnableInternalRegulator
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[81]"></a>LL_ADC_GetCommonPathInternalCh</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_GetCommonPathInternalCh))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_GetCommonPathInternalCh
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
</UL>

<P><STRONG><a name="[c0]"></a>LL_ADC_GetMultiDMATransfer</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_GetMultiDMATransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_GetMultiDMATransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[b8]"></a>LL_ADC_GetMultimode</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_GetMultimode))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_GetMultimode
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[b4]"></a>LL_ADC_GetOffsetChannel</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_GetOffsetChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LL_ADC_GetOffsetChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[ae]"></a>LL_ADC_INJ_IsConversionOngoing</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_INJ_IsConversionOngoing))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_INJ_IsConversionOngoing
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[bc]"></a>LL_ADC_INJ_IsTriggerSourceSWStart</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_INJ_IsTriggerSourceSWStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_INJ_IsTriggerSourceSWStart
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[c5]"></a>LL_ADC_IsDeepPowerDownEnabled</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_IsDeepPowerDownEnabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_IsDeepPowerDownEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[7b]"></a>LL_ADC_IsDisableOngoing</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_IsDisableOngoing))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_IsDisableOngoing
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Disable
</UL>

<P><STRONG><a name="[7c]"></a>LL_ADC_IsEnabled</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_IsEnabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_IsEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Disable
</UL>

<P><STRONG><a name="[c7]"></a>LL_ADC_IsInternalRegulatorEnabled</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_IsInternalRegulatorEnabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_IsInternalRegulatorEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[ac]"></a>LL_ADC_REG_IsConversionOngoing</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_REG_IsConversionOngoing))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_REG_IsConversionOngoing
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[ba]"></a>LL_ADC_REG_IsTriggerSourceSWStart</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_REG_IsTriggerSourceSWStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_REG_IsTriggerSourceSWStart
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[ad]"></a>LL_ADC_REG_SetSequencerRanks</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_REG_SetSequencerRanks))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LL_ADC_REG_SetSequencerRanks
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[d1]"></a>LL_ADC_REG_StartConversion</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_REG_StartConversion))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_REG_StartConversion
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start
</UL>

<P><STRONG><a name="[af]"></a>LL_ADC_SetChannelSamplingTime</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_SetChannelSamplingTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LL_ADC_SetChannelSamplingTime
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[b6]"></a>LL_ADC_SetChannelSingleDiff</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_SetChannelSingleDiff))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LL_ADC_SetChannelSingleDiff
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[c9]"></a>LL_ADC_SetCommonClock</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_SetCommonClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LL_ADC_SetCommonClock
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[b7]"></a>LL_ADC_SetCommonPathInternalCh</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_SetCommonPathInternalCh))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LL_ADC_SetCommonPathInternalCh
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[b1]"></a>LL_ADC_SetOffset</STRONG> (Thumb, 60 bytes, Stack size 20 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_SetOffset))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = LL_ADC_SetOffset
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[b3]"></a>LL_ADC_SetOffsetSaturation</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_SetOffsetSaturation))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LL_ADC_SetOffsetSaturation
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[b2]"></a>LL_ADC_SetOffsetSign</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_SetOffsetSign))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LL_ADC_SetOffsetSign
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[b5]"></a>LL_ADC_SetOffsetState</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_SetOffsetState))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LL_ADC_SetOffsetState
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[b0]"></a>LL_ADC_SetSamplingTimeCommonConfig</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32g4xx_hal_adc.o(.text.LL_ADC_SetSamplingTimeCommonConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LL_ADC_SetSamplingTimeCommonConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[a1]"></a>LL_ADC_GetCommonPathInternalCh</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_GetCommonPathInternalCh))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_GetCommonPathInternalCh
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[a6]"></a>LL_ADC_GetMultimode</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_GetMultimode))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_GetMultimode
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart_IT
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart
</UL>

<P><STRONG><a name="[9d]"></a>LL_ADC_GetOffsetChannel</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_GetOffsetChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LL_ADC_GetOffsetChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[a7]"></a>LL_ADC_INJ_GetTrigAuto</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_INJ_GetTrigAuto))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_INJ_GetTrigAuto
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart_IT
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart
</UL>

<P><STRONG><a name="[96]"></a>LL_ADC_INJ_IsConversionOngoing</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_INJ_IsConversionOngoing))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_INJ_IsConversionOngoing
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart_IT
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart
</UL>

<P><STRONG><a name="[a8]"></a>LL_ADC_INJ_StartConversion</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_INJ_StartConversion))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_INJ_StartConversion
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart_IT
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedStart
</UL>

<P><STRONG><a name="[94]"></a>LL_ADC_IsCalibrationOnGoing</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_IsCalibrationOnGoing))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_IsCalibrationOnGoing
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_Calibration_Start
</UL>

<P><STRONG><a name="[9f]"></a>LL_ADC_IsEnabled</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_IsEnabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_IsEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_MultiModeConfigChannel
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[97]"></a>LL_ADC_REG_IsConversionOngoing</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_REG_IsConversionOngoing))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_ADC_REG_IsConversionOngoing
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_MultiModeConfigChannel
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[98]"></a>LL_ADC_SetChannelSamplingTime</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_SetChannelSamplingTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LL_ADC_SetChannelSamplingTime
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[a0]"></a>LL_ADC_SetChannelSingleDiff</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_SetChannelSingleDiff))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LL_ADC_SetChannelSingleDiff
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[a2]"></a>LL_ADC_SetCommonPathInternalCh</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_SetCommonPathInternalCh))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LL_ADC_SetCommonPathInternalCh
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[9a]"></a>LL_ADC_SetOffset</STRONG> (Thumb, 60 bytes, Stack size 20 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_SetOffset))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = LL_ADC_SetOffset
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[9c]"></a>LL_ADC_SetOffsetSaturation</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_SetOffsetSaturation))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LL_ADC_SetOffsetSaturation
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[9b]"></a>LL_ADC_SetOffsetSign</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_SetOffsetSign))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LL_ADC_SetOffsetSign
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[9e]"></a>LL_ADC_SetOffsetState</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_SetOffsetState))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LL_ADC_SetOffsetState
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[99]"></a>LL_ADC_SetSamplingTimeCommonConfig</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_SetSamplingTimeCommonConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LL_ADC_SetSamplingTimeCommonConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConfigChannel
</UL>

<P><STRONG><a name="[93]"></a>LL_ADC_StartCalibration</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32g4xx_hal_adc_ex.o(.text.LL_ADC_StartCalibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LL_ADC_StartCalibration
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_Calibration_Start
</UL>

<P><STRONG><a name="[106]"></a>RCC_GetSysClockFreqFromPLLSource</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, stm32g4xx_hal_rcc.o(.text.RCC_GetSysClockFreqFromPLLSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCC_GetSysClockFreqFromPLLSource
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[e4]"></a>DMA_CalcDMAMUXChannelBaseAndMask</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, stm32g4xx_hal_dma.o(.text.DMA_CalcDMAMUXChannelBaseAndMask))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_CalcDMAMUXChannelBaseAndMask
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[e5]"></a>DMA_CalcDMAMUXRequestGenBaseAndMask</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, stm32g4xx_hal_dma.o(.text.DMA_CalcDMAMUXRequestGenBaseAndMask))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_CalcDMAMUXRequestGenBaseAndMask
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[e7]"></a>DMA_SetConfig</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, stm32g4xx_hal_dma.o(.text.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[100]"></a>NVIC_EncodePriority</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, stm32g4xx_hal_cortex.o(.text.NVIC_EncodePriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = NVIC_EncodePriority
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[10b]"></a>SysTick_Config</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, stm32g4xx_hal_cortex.o(.text.SysTick_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SysTick_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[fe]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 52 bytes, Stack size 4 bytes, stm32g4xx_hal_cortex.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>

<P><STRONG><a name="[ff]"></a>__NVIC_GetPriorityGrouping</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32g4xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[101]"></a>__NVIC_SetPriority</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, stm32g4xx_hal_cortex.o(.text.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
</UL>

<P><STRONG><a name="[102]"></a>__NVIC_SetPriorityGrouping</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, stm32g4xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>

<P><STRONG><a name="[d3]"></a>LL_EXTI_ClearFlag_0_31</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_ClearFlag_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_ClearFlag_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_IRQHandler
</UL>

<P><STRONG><a name="[dc]"></a>LL_EXTI_DisableEvent_0_31</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_DisableEvent_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_DisableEvent_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>

<P><STRONG><a name="[da]"></a>LL_EXTI_DisableFallingTrig_0_31</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_DisableFallingTrig_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_DisableFallingTrig_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>

<P><STRONG><a name="[de]"></a>LL_EXTI_DisableIT_0_31</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_DisableIT_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_DisableIT_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>

<P><STRONG><a name="[d8]"></a>LL_EXTI_DisableRisingTrig_0_31</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_DisableRisingTrig_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_DisableRisingTrig_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>

<P><STRONG><a name="[db]"></a>LL_EXTI_EnableEvent_0_31</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_EnableEvent_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_EnableEvent_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>

<P><STRONG><a name="[d9]"></a>LL_EXTI_EnableFallingTrig_0_31</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_EnableFallingTrig_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_EnableFallingTrig_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>

<P><STRONG><a name="[dd]"></a>LL_EXTI_EnableIT_0_31</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_EnableIT_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_EnableIT_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>

<P><STRONG><a name="[d7]"></a>LL_EXTI_EnableRisingTrig_0_31</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_EnableRisingTrig_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_EnableRisingTrig_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_Init
</UL>

<P><STRONG><a name="[d2]"></a>LL_EXTI_IsActiveFlag_0_31</STRONG> (Thumb, 30 bytes, Stack size 4 bytes, stm32g4xx_hal_comp.o(.text.LL_EXTI_IsActiveFlag_0_31))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = LL_EXTI_IsActiveFlag_0_31
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_COMP_IRQHandler
</UL>

<P><STRONG><a name="[f6]"></a>FDCAN_CalcultateRamBlockAddresses</STRONG> (Thumb, 158 bytes, Stack size 12 bytes, stm32g4xx_hal_fdcan.o(.text.FDCAN_CalcultateRamBlockAddresses))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = FDCAN_CalcultateRamBlockAddresses
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FDCAN_Init
</UL>

<P><STRONG><a name="[11c]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, stm32g4xx_hal_tim.o(.text.TIM_ITRx_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_ITRx_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[12d]"></a>TIM_OC1_SetConfig</STRONG> (Thumb, 340 bytes, Stack size 20 bytes, stm32g4xx_hal_tim.o(.text.TIM_OC1_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[12e]"></a>TIM_OC3_SetConfig</STRONG> (Thumb, 372 bytes, Stack size 20 bytes, stm32g4xx_hal_tim.o(.text.TIM_OC3_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC3_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[12f]"></a>TIM_OC4_SetConfig</STRONG> (Thumb, 374 bytes, Stack size 20 bytes, stm32g4xx_hal_tim.o(.text.TIM_OC4_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC4_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[130]"></a>TIM_OC5_SetConfig</STRONG> (Thumb, 210 bytes, Stack size 20 bytes, stm32g4xx_hal_tim.o(.text.TIM_OC5_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC5_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[131]"></a>TIM_OC6_SetConfig</STRONG> (Thumb, 212 bytes, Stack size 20 bytes, stm32g4xx_hal_tim.o(.text.TIM_OC6_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC6_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[11b]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, stm32g4xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[11d]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 82 bytes, Stack size 20 bytes, stm32g4xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[115]"></a>TIM_CCxNChannelCmd</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32g4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_CCxNChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_PWMN_Stop
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_PWMN_Start
</UL>

<P><STRONG><a name="[52]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, stm32g4xx_hal_uart.o(.text.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = UART_DMAAbortOnError &rArr; HAL_UART_ErrorCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[55]"></a>UART_DMAError</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, stm32g4xx_hal_uart.o(.text.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = UART_DMAError &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
</UL>
<P><STRONG><a name="[53]"></a>UART_DMATransmitCplt</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, stm32g4xx_hal_uart.o(.text.UART_DMATransmitCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = UART_DMATransmitCplt &rArr; HAL_UART_TxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
</UL>
<P><STRONG><a name="[54]"></a>UART_DMATxHalfCplt</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, stm32g4xx_hal_uart.o(.text.UART_DMATxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = UART_DMATxHalfCplt &rArr; HAL_UART_TxHalfCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
</UL>
<P><STRONG><a name="[13a]"></a>UART_EndRxTransfer</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, stm32g4xx_hal_uart.o(.text.UART_EndRxTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[140]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, stm32g4xx_hal_uart.o(.text.UART_EndTransmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = UART_EndTransmit_IT &rArr; HAL_UART_TxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[163]"></a>UART_EndTxTransfer</STRONG> (Thumb, 92 bytes, Stack size 12 bytes, stm32g4xx_hal_uart.o(.text.UART_EndTxTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = UART_EndTxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[137]"></a>UARTEx_SetNbDataToProcess</STRONG> (Thumb, 136 bytes, Stack size 8 bytes, stm32g4xx_hal_uart_ex.o(.text.UARTEx_SetNbDataToProcess))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
