Dependencies for Project 'STM32G4_GPIO', Target 'STM32G4_GPIO': (DO NOT MODIFY !)
CompilerVersion: 6140000::V6.14::ARMCLANG
F (startup_stm32g431xx.s)(0x668A7C57)(--target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -masm=auto -c

-gdwarf-3

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-Wa,armasm,--pd,"__UVISION_VERSION SETA 531" -Wa,armasm,--pd,"_RTE_ SETA 1" -Wa,armasm,--pd,"STM32G431xx SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o stm32g4_gpio/startup_stm32g431xx.o)
F (../Core/Src/main.c)(0x66CB0A28)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/main.o -MD)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
I (..\Core\Inc\adc.h)(0x668A7C54)
I (..\Core\Inc\comp.h)(0x668A7C55)
I (..\Core\Inc\dac.h)(0x668A7C55)
I (..\Core\Inc\dma.h)(0x668A7C55)
I (..\Core\Inc\fdcan.h)(0x668A7C55)
I (..\Core\Inc\opamp.h)(0x668A7C55)
I (..\Core\Inc\tim.h)(0x668A7C55)
I (..\Core\Inc\usart.h)(0x668A7C55)
I (..\Core\Inc\gpio.h)(0x668A7C53)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdio.h)(0x5ED60BE4)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\string.h)(0x5ED60BE4)
I (..\MATLAB\FOC_Model.h)(0x663A3542)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\math.h)(0x5ED60BE4)
I (..\MATLAB\rtwtypes.h)(0x663A3542)
I (..\app\uart_comm_func.h)(0x668A1955)
I (..\app\motor_struct.h)(0x66CB0D14)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\ctype.h)(0x5ED60BE4)
I (..\app\hall_speed_pos_fdbk.h)(0x66CAD677)
F (../Core/Src/gpio.c)(0x668A7C53)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/gpio.o -MD)
I (..\Core\Inc\gpio.h)(0x668A7C53)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/adc.c)(0x668A7C54)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/adc.o -MD)
I (..\Core\Inc\adc.h)(0x668A7C54)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/comp.c)(0x668A7C54)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/comp.o -MD)
I (..\Core\Inc\comp.h)(0x668A7C55)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/dac.c)(0x668A7C55)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/dac.o -MD)
I (..\Core\Inc\dac.h)(0x668A7C55)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/dma.c)(0x668A7C55)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/dma.o -MD)
I (..\Core\Inc\dma.h)(0x668A7C55)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/fdcan.c)(0x668A7C55)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/fdcan.o -MD)
I (..\Core\Inc\fdcan.h)(0x668A7C55)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/opamp.c)(0x668A7C55)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/opamp.o -MD)
I (..\Core\Inc\opamp.h)(0x668A7C55)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/tim.c)(0x66CB093D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/tim.o -MD)
I (..\Core\Inc\tim.h)(0x668A7C55)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/usart.c)(0x668A7C55)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/usart.o -MD)
I (..\Core\Inc\usart.h)(0x668A7C55)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/stm32g4xx_it.c)(0x668E8E0E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_it.o -MD)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_it.h)(0x668A7C55)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdio.h)(0x5ED60BE4)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\string.h)(0x5ED60BE4)
I (..\MATLAB\FOC_Model.h)(0x663A3542)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\math.h)(0x5ED60BE4)
I (..\MATLAB\rtwtypes.h)(0x663A3542)
I (..\app\motor_board_func.h)(0x66CAD6C1)
I (..\app\uart_comm_func.h)(0x668A1955)
I (..\app\motor_struct.h)(0x66CB0D14)
I (..\Core\Inc\usart.h)(0x668A7C55)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\ctype.h)(0x5ED60BE4)
I (..\app\hall_speed_pos_fdbk.h)(0x66CAD677)
I (..\Core\Inc\tim.h)(0x668A7C55)
F (../Core/Src/stm32g4xx_hal_msp.c)(0x668A7C55)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_msp.o -MD)
I (..\Core\Inc\main.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (..\Middlewares\ST\ARM\DSP\Lib\arm_cortexM4lf_math.lib)(0x668A3DAF)()
F (..\MATLAB\FOC_Model.c)(0x663A3AE0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/foc_model.o -MD)
I (..\MATLAB\FOC_Model.h)(0x663A3542)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\math.h)(0x5ED60BE4)
I (..\MATLAB\rtwtypes.h)(0x663A3542)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\limits.h)(0x5ED60BE4)
F (..\app\hall_speed_pos_fdbk.c)(0x66CB093D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/hall_speed_pos_fdbk.o -MD)
I (..\app\hall_speed_pos_fdbk.h)(0x66CAD677)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
I (..\app\motor_struct.h)(0x66CB0D14)
I (..\Core\Inc\tim.h)(0x668A7C55)
I (..\Core\Inc\main.h)(0x668A7C55)
F (..\app\motor_struct.c)(0x66909BFE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/motor_struct.o -MD)
I (..\app\motor_struct.h)(0x66CB0D14)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (..\app\motor_board_func.c)(0x685A794A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/motor_board_func.o -MD)
I (..\app\motor_board_func.h)(0x66CAD6C1)
I (..\app\uart_comm_func.h)(0x668A1955)
I (..\app\motor_struct.h)(0x66CB0D14)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
I (..\Core\Inc\usart.h)(0x668A7C55)
I (..\Core\Inc\main.h)(0x668A7C55)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdio.h)(0x5ED60BE4)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\string.h)(0x5ED60BE4)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\ctype.h)(0x5ED60BE4)
I (..\app\hall_speed_pos_fdbk.h)(0x66CAD677)
I (..\Core\Inc\tim.h)(0x668A7C55)
I (..\Core\Inc\adc.h)(0x668A7C54)
I (..\MATLAB\FOC_Model.h)(0x663A3542)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\math.h)(0x5ED60BE4)
I (..\MATLAB\rtwtypes.h)(0x663A3542)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_adc.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_adc_ex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_adc.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_ll_adc.o -MD)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_rcc.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_rcc_ex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_flash.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_flash_ex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_flash_ramfunc.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_gpio.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_exti.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_dma.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_dma_ex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_pwr.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_pwr_ex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_cortex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_comp.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_comp.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dac.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_dac.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dac_ex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_dac_ex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_fdcan.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_fdcan.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_opamp.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_opamp.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_opamp_ex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_opamp_ex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_tim.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_tim_ex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_uart.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c)(0x6677B998)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/stm32g4xx_hal_uart_ex.o -MD)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
F (../Core/Src/system_stm32g4xx.c)(0x63DF2A50)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/ST/ARM/DSP/Inc -I ../MATLAB -I ../app

-I./RTE/_STM32G4_GPIO

-I"E:/encode_elevant software/keil5/keil_ARM_pack/ARM/CMSIS/5.7.0/CMSIS/Core/Include"

-I"E:/encode_elevant software/keil5/keil_ARM_pack/Keil/STM32G4xx_DFP/1.4.0/Drivers/CMSIS/Device/ST/STM32G4xx/Include"

-D__UVISION_VERSION="531" -D_RTE_ -DSTM32G431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G431xx -D__TARGET_FPU_VFP -D__FPU_PRESENT -DARM_MATH_CM4

-o stm32g4_gpio/system_stm32g4xx.o -MD)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g431xx.h)(0x6677B998)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stdint.h)(0x5ED60BE4)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6677B953)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6677B953)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_compat.h)(0x5E0CC2EC)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\arm_acle.h)(0x5E0CC318)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6677B953)
I (..\Drivers\CMSIS\Device\ST\STM32G4xx\Include\system_stm32g4xx.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal.h)(0x6677B998)
I (..\Core\Inc\stm32g4xx_hal_conf.h)(0x668A7C55)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_def.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6677B998)
I (E:\encode_elevant software\keil5\install_keil5\ARM\ARMCLANG\include\stddef.h)(0x5ED60BE4)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_rcc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_gpio_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dma_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_cortex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_adc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_adc_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_comp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_ll_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_dac_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_exti.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_fdcan.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_flash_ramfunc.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_opamp_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_pwr_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_tim_ex.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart.h)(0x6677B998)
I (..\Drivers\STM32G4xx_HAL_Driver\Inc\stm32g4xx_hal_uart_ex.h)(0x6677B998)
